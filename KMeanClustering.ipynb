





import numpy as np
import matplotlib.pyplot as plt
import random


def createClusterData(N,k):
    random.seed(10)
    pointsPerCluster = float(N)/k

    X=[]

    for i in range(k):
        incomeCentroid = random.unoform(20000.0,200000.0)
        ageCentroid = random.uniform(20.0,70.0)
        for j in range(int(pointsPerCluster)):
            x.append(np.random.normal(incomeCentroid,10000.0),np.random.normal(ageCentroid,2.0))
    
    X = np.array(X)

    return(X)


print(createClusterData(100,5))